{"version": 3, "file": "optional.js", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/optional.ts"], "names": [], "mappings": ";;;AACA,6CAAwD;AAGjD,MAAM,gBAAgB,GAAG,CAAC,GAAmB,EAAE,IAAU,EAA+B,EAAE;IAC/F,IACE,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAC/F,CAAC;QACD,OAAO,IAAA,mBAAQ,EAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAClF,CAAC;IAED,MAAM,WAAW,GAAG,IAAA,mBAAQ,EAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;QAC/C,GAAG,IAAI;QACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,CAAC;KACjD,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC,CAAC;QAChB;YACE,KAAK,EAAE;gBACL;oBACE,GAAG,EAAE,EAAE;iBACR;gBACD,WAAW;aACZ;SACF;QACH,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC;AAvBW,QAAA,gBAAgB,oBAuB3B"}