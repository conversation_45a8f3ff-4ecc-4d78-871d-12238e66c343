{"version": 3, "file": "pagination.d.ts", "sourceRoot": "", "sources": ["../src/core/pagination.ts"], "names": [], "mappings": "OAGO,EAAE,mBAAmB,EAAE;OAEvB,EAAE,UAAU,EAAE;OACd,EAAE,KAAK,MAAM,EAAE;OACf,EAAE,KAAK,gBAAgB,EAAE;AAGhC,MAAM,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,EAAE,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC;AAE7G,8BAAsB,YAAY,CAAC,IAAI,CAAE,YAAW,aAAa,CAAC,IAAI,CAAC;;IAErE,SAAS,CAAC,OAAO,EAAE,mBAAmB,CAAC;IAEvC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC7B,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC;gBAEZ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,mBAAmB;IAO3F,QAAQ,CAAC,sBAAsB,IAAI,kBAAkB,GAAG,IAAI;IAE5D,QAAQ,CAAC,iBAAiB,IAAI,IAAI,EAAE;IAEpC,WAAW,IAAI,OAAO;IAMhB,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;IAW3B,SAAS,IAAI,cAAc,CAAC,IAAI,CAAC;IASjC,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC;CAOtD;AAED;;;;;;;;GAQG;AACH,qBAAa,WAAW,CACpB,SAAS,SAAS,YAAY,CAAC,IAAI,CAAC,EACpC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAE3D,SAAQ,UAAU,CAAC,SAAS,CAC5B,YAAW,aAAa,CAAC,IAAI,CAAC;gBAG5B,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,OAAO,CAAC,gBAAgB,CAAC,EAClC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,qBAAqB,CAAC,OAAO,YAAY,CAAC,KAAK,SAAS;IAe9E;;;;;;OAMG;IACI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC;CAMtD;AAED,MAAM,WAAW,YAAY,CAAC,IAAI;IAChC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,MAAM,EAAE,MAAM,CAAC;CAChB;AAED;;GAEG;AACH,qBAAa,IAAI,CAAC,IAAI,CAAE,SAAQ,YAAY,CAAC,IAAI,CAAE,YAAW,YAAY,CAAC,IAAI,CAAC;IAC9E,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,MAAM,EAAE,MAAM,CAAC;gBAEH,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mBAAmB;IAOtG,iBAAiB,IAAI,IAAI,EAAE;IAI3B,sBAAsB,IAAI,kBAAkB,GAAG,IAAI;CAGpD;AAED,MAAM,WAAW,kBAAkB,CAAC,IAAI;IACtC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,QAAQ,EAAE,OAAO,CAAC;CACnB;AAED,MAAM,WAAW,gBAAgB;IAC/B,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,UAAU,CAAC,IAAI,SAAS;IAAE,EAAE,EAAE,MAAM,CAAA;CAAE,CACjD,SAAQ,YAAY,CAAC,IAAI,CACzB,YAAW,kBAAkB,CAAC,IAAI,CAAC;IAEnC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,QAAQ,EAAE,OAAO,CAAC;gBAGhB,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAE,kBAAkB,CAAC,IAAI,CAAC,EAC9B,OAAO,EAAE,mBAAmB;IAQ9B,iBAAiB,IAAI,IAAI,EAAE;IAIlB,WAAW,IAAI,OAAO;IAQ/B,sBAAsB,IAAI,kBAAkB,GAAG,IAAI;CAepD;AAED,MAAM,WAAW,8BAA8B,CAAC,IAAI;IAClD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,QAAQ,EAAE,OAAO,CAAC;IAElB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,4BAA4B;IAC3C,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,sBAAsB,CAAC,IAAI,CACtC,SAAQ,YAAY,CAAC,IAAI,CACzB,YAAW,8BAA8B,CAAC,IAAI,CAAC;IAE/C,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAElB,QAAQ,EAAE,OAAO,CAAC;IAElB,OAAO,EAAE,MAAM,CAAC;gBAGd,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAE,8BAA8B,CAAC,IAAI,CAAC,EAC1C,OAAO,EAAE,mBAAmB;IAS9B,iBAAiB,IAAI,IAAI,EAAE;IAIlB,WAAW,IAAI,OAAO;IAQ/B,sBAAsB,IAAI,kBAAkB,GAAG,IAAI;CAcpD"}