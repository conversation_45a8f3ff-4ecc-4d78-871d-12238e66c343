import express from "express";
import dotenv from "dotenv";
import { chatWithAI } from "./src/services/gemini.js";

dotenv.config();

const app = express();
const PORT = 3001; // Use different port for testing

app.use(express.json());

// Test route
app.post("/test-chat", async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    console.log(`📝 Received message: ${message}`);
    const response = await chatWithAI(message);
    console.log(`🤖 AI Response: ${response.substring(0, 100)}...`);
    
    res.json({ reply: response });
  } catch (error) {
    console.error("❌ Error in /test-chat:", error);
    res.status(500).json({ error: "Something went wrong", details: error.message });
  }
});

// Health check
app.get("/health", (req, res) => {
  res.json({ status: "OK", message: "Gemini AI Server is running!" });
});

app.listen(PORT, () => {
  console.log(`🚀 Test Server running on http://localhost:${PORT}`);
  console.log(`📋 Test endpoints:`);
  console.log(`   GET  http://localhost:${PORT}/health`);
  console.log(`   POST http://localhost:${PORT}/test-chat`);
  console.log(`\n🧪 Test with curl:`);
  console.log(`   curl -X POST http://localhost:${PORT}/test-chat -H "Content-Type: application/json" -d "{\\"message\\": \\"Hello AI!\\"}"`);
});
