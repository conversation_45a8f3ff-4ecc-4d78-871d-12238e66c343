import { chatWithAI } from "./src/services/gemini.js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Check if API key is set
if (!process.env.GEMINI_API_KEY) {
  console.error("❌ GEMINI_API_KEY not found in environment variables!");
  console.log("💡 Please add your Gemini API key to the .env file:");
  console.log("   GEMINI_API_KEY=your_api_key_here");
  console.log("💡 Get your API key from: https://aistudio.google.com/app/apikey");
  process.exit(1);
}

async function testAIModel() {
  console.log("🧪 Testing Gemini AI Model...");
  console.log("=" .repeat(50));

  try {
    // Test 1: Simple greeting
    console.log("Test 1: Simple greeting");
    const response1 = await chatWithAI("Hello! Can you help me with my studies?");
    console.log("✅ Response:", response1);
    console.log("-".repeat(50));

    // Test 2: Math question
    console.log("Test 2: Math question");
    const response2 = await chatWithAI("What is 15 + 27?");
    console.log("✅ Response:", response2);
    console.log("-".repeat(50));

    // Test 3: Study-related question
    console.log("Test 3: Study-related question");
    const response3 = await chatWithAI("Can you explain what photosynthesis is in simple terms?");
    console.log("✅ Response:", response3);
    console.log("-".repeat(50));

    console.log("🎉 All tests passed! Gemini AI model is working correctly.");

  } catch (error) {
    console.error("❌ Test failed:", error.message);

    // Check common issues
    if (error.message.includes("API key") || error.message.includes("authentication")) {
      console.log("💡 Tip: Make sure your GEMINI_API_KEY is set correctly in the .env file");
      console.log("💡 Get your API key from: https://aistudio.google.com/app/apikey");
    } else if (error.message.includes("network") || error.message.includes("fetch")) {
      console.log("💡 Tip: Check your internet connection");
    } else if (error.message.includes("quota") || error.message.includes("billing")) {
      console.log("💡 Tip: Check your Google AI Studio usage limits");
    }
  }
}

// Run the test
testAIModel();
